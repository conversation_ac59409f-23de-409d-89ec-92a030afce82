@inherits LayoutComponentBase
@using Intra2025.Components.Base
@using Intra2025.Servervices
@inject UserState _userState
@inject SsoService sso
@inject IJSRuntime JSRuntime
@inject ILogger<MainLayout> Logger

<div class="page">
    <header class="top-header">
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand"
                    href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/'))/">支付作業登打系統</a>


                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link"
                                href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/'))/ComRemit/PayeeList">
                                【匯款資料維護(step1)】
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link"
                                href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/'))/ComRemit/RemitMt">
                                【匯款整彙作業(step2)】
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link"
                                href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/'))/ComRemit/RemitedList">
                                【已產彙清單(step3)】
                            </a>
                        </li>
                        @if (_userState.IsAdmin)
                        {
                            <li class="nav-item">
                                <a class="nav-link"
                                    href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/'))/ComRemit/AdminList">
                                    【管理者清單】
                                </a>
                            </li>
                        }

                        <!-- 使用者資訊 -->
                        <div class="navbar-text text-white me-3">
                            <span class="badge text-white"
                                style="background-color:#127681; border-radius: 10px;font-size:14px">
                                【@_userState.UnitName-@_userState.UserName】
                            </span>
                        </div>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="content">
        @Body
    </main>
</div>

<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>

@code {
    protected override void OnInitialized()
    {
        base.OnInitialized();

        // 自動執行 SsoService 初始化 UserState
        try
        {
            Logger.LogInformation(DateTime.Now.ToString() + " ##### MainLayout 開始驗證和初始化 SSO 用戶 #####");
            sso.ValidateAndInitializeUser();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "MainLayout SSO 驗證失敗: {Message}", ex.Message);
        }
    }
}
